{"ContentRoots": ["C:\\Users\\<USER>\\.nuget\\packages\\blazordaterangepicker\\5.3.0\\staticwebassets\\", "C:\\Users\\<USER>\\.nuget\\packages\\blazor.bootstrap\\3.2.0\\staticwebassets\\", "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\wwwroot\\", "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\", "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Libraries\\Core.Themis.Libraries.Razor\\Core.Themis.Libraries.Razor\\wwwroot\\", "D:\\WORK\\CORE_DEV\\Themis_core_DEV\\Widgets\\Core.Themis.Widgets.Offers\\Core.Themis.Widgets.Offers\\obj\\Debug\\net8.0\\scopedcss\\bundle\\"], "Root": {"Children": {"_content": {"Children": {"BlazorDateRangePicker": {"Children": {"clickAndPositionHandler.js": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "clickAndPositionHandler.js"}, "Patterns": null}, "BlazorDateRangePicker.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 0, "SubPath": "BlazorDateRangePicker.bundle.scp.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Blazor.Bootstrap": {"Children": {"blazor.bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.css"}, "Patterns": null}, "blazor.bootstrap.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.js"}, "Patterns": null}, "blazor.bootstrap.ai.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.ai.js"}, "Patterns": null}, "blazor.bootstrap.pdf.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.pdf.js"}, "Patterns": null}, "blazor.bootstrap.sortable-list.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.sortable-list.js"}, "Patterns": null}, "blazor.bootstrap.theme-switcher.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "blazor.bootstrap.theme-switcher.js"}, "Patterns": null}, "icon": {"Children": {"128X128.png": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "icon/128X128.png"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "pdfjs-4.0.379.min.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "pdfjs-4.0.379.min.js"}, "Patterns": null}, "pdfjs-4.0.379.worker.min.js": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "pdfjs-4.0.379.worker.min.js"}, "Patterns": null}, "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 1, "SubPath": "Blazor.Bootstrap.7t9tbfaemk.bundle.scp.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Core.Themis.Libraries.Razor": {"Children": {"Common": {"Children": {"Components": {"Children": {"Inputs": {"Children": {"InputImageFileCustom.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Inputs/InputImageFileCustom.razor.js"}, "Patterns": null}, "InputTextAreaCustom.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Inputs/InputTextAreaCustom.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Select": {"Children": {"NewSelect2.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Select/NewSelect2.razor.js"}, "Patterns": null}, "Select2.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Select/Select2.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Sortable": {"Children": {"SortableList.razor.js": {"Children": null, "Asset": {"ContentRootIndex": 3, "SubPath": "Common/Components/Sortable/SortableList.razor.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "Core.Themis.Libraries.Razor.rpcwcmxhht.bundle.scp.css": {"Children": null, "Asset": {"ContentRootIndex": 4, "SubPath": "Core.Themis.Libraries.Razor.bundle.scp.css"}, "Patterns": null}, "css": {"Children": {"common.less": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/common.less"}, "Patterns": null}, "widget.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget.css"}, "Patterns": null}, "widget-modules": {"Children": {"button.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/button.css"}, "Patterns": null}, "loader.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/loader.css"}, "Patterns": null}, "modal.css": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "css/widget-modules/modal.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"common_razor_library.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/common_razor_library.js"}, "Patterns": null}, "phoneInput.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/phoneInput.js"}, "Patterns": null}, "widget.js": {"Children": null, "Asset": {"ContentRootIndex": 5, "SubPath": "js/widget.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 5, "Pattern": "**", "Depth": 2}]}}, "Asset": null, "Patterns": null}, "css": {"Children": {"Basket": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/Basket/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "catalog": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/catalog/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "CrossSelling": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/CrossSelling/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "fontawesome": {"Children": {"brands.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/fontawesome/brands.min.css"}, "Patterns": null}, "fontawesome.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/fontawesome/fontawesome.min.css"}, "Patterns": null}, "solid.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/fontawesome/solid.min.css"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "HomeModular": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/HomeModular/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Insurance": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/Insurance/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Product": {"Children": {"ProductDetails": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/Product/ProductDetails/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/Product/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Session": {"Children": {"style.less": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/Session/style.less"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "webfonts": {"Children": {"fa-brands-400.ttf": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-brands-400.ttf"}, "Patterns": null}, "fa-brands-400.woff2": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-brands-400.woff2"}, "Patterns": null}, "fa-regular-400.ttf": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-regular-400.ttf"}, "Patterns": null}, "fa-regular-400.woff2": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-regular-400.woff2"}, "Patterns": null}, "fa-solid-900.ttf": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-solid-900.ttf"}, "Patterns": null}, "fa-solid-900.woff2": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-solid-900.woff2"}, "Patterns": null}, "fa-v4compatibility.ttf": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-v4compatibility.ttf"}, "Patterns": null}, "fa-v4compatibility.woff2": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "css/webfonts/fa-v4compatibility.woff2"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "favicon.ico": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "favicon.ico"}, "Patterns": null}, "img": {"Children": {"angle_button_white.svg": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "img/angle_button_white.svg"}, "Patterns": null}, "sessionnotavailable.png": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "img/sessionnotavailable.png"}, "Patterns": null}, "ticket-shape-bottom.svg": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "img/ticket-shape-bottom.svg"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"basket": {"Children": {"basket.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/basket/basket.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "bootstrap-spinner": {"Children": {"bootstrap-input-spinner.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/bootstrap-spinner/bootstrap-input-spinner.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "catalog": {"Children": {"catalog.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/catalog/catalog.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "categprices": {"Children": {"categprices.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/categprices/categprices.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "commons.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/commons.js"}, "Patterns": null}, "crossSelling": {"Children": {"crossSelling.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/crossSelling/crossSelling.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "feedbook": {"Children": {"feedbookForm.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/feedbook/feedbookForm.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "homemodular": {"Children": {"homemodular.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/homemodular/homemodular.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "insurance": {"Children": {"insurance.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/insurance/insurance.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "lessModify.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/lessModify.js"}, "Patterns": null}, "product": {"Children": {"product.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/product/product.js"}, "Patterns": null}, "productDetails": {"Children": {"productDetailsCADH.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/product/productDetails/productDetailsCADH.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "seatsSelection": {"Children": {"seatsSelection.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/seatsSelection/seatsSelection.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "session": {"Children": {"fix-nodisponible.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/session/fix-nodisponible.js"}, "Patterns": null}, "pano.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/session/pano.js"}, "Patterns": null}, "seatplan.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/session/seatplan.js"}, "Patterns": null}, "session.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/session/session.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "site.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/site.js"}, "Patterns": null}, "tunnel": {"Children": {"tunnel.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/tunnel/tunnel.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "widget.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "js/widget.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "lib": {"Children": {"bootstrap": {"Children": {"dist": {"Children": {"css": {"Children": {"bootstrap-grid.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css"}, "Patterns": null}, "bootstrap-grid.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, "Patterns": null}, "bootstrap-grid.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, "Patterns": null}, "bootstrap-grid.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, "Patterns": null}, "bootstrap-reboot.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, "Patterns": null}, "bootstrap-reboot.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, "Patterns": null}, "bootstrap-reboot.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, "Patterns": null}, "bootstrap-reboot.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, "Patterns": null}, "bootstrap.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap.css"}, "Patterns": null}, "bootstrap.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap.css.map"}, "Patterns": null}, "bootstrap.min.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css"}, "Patterns": null}, "bootstrap.min.css.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/css/bootstrap.min.css.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "js": {"Children": {"bootstrap.bundle.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, "Patterns": null}, "bootstrap.bundle.js.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, "Patterns": null}, "bootstrap.bundle.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, "Patterns": null}, "bootstrap.bundle.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, "Patterns": null}, "bootstrap.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.js"}, "Patterns": null}, "bootstrap.js.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.js.map"}, "Patterns": null}, "bootstrap.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js"}, "Patterns": null}, "bootstrap.min.js.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/bootstrap/LICENSE"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery-validation-unobtrusive": {"Children": {"jquery.validate.unobtrusive.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.js"}, "Patterns": null}, "jquery.validate.unobtrusive.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"}, "Patterns": null}, "LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery-validation": {"Children": {"dist": {"Children": {"additional-methods.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation/dist/additional-methods.js"}, "Patterns": null}, "additional-methods.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation/dist/additional-methods.min.js"}, "Patterns": null}, "jquery.validate.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation/dist/jquery.validate.js"}, "Patterns": null}, "jquery.validate.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation/dist/jquery.validate.min.js"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE.md": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery-validation/LICENSE.md"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "jquery": {"Children": {"dist": {"Children": {"jquery.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery/dist/jquery.js"}, "Patterns": null}, "jquery.min.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery/dist/jquery.min.js"}, "Patterns": null}, "jquery.min.map": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery/dist/jquery.min.map"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "LICENSE.txt": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "lib/jquery/LICENSE.txt"}, "Patterns": null}}, "Asset": null, "Patterns": null}}, "Asset": null, "Patterns": null}, "websiteexterneDemo": {"Children": {"testingCallOffersLocal.html": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/testingCallOffersLocal.html"}, "Patterns": null}, "websiteexterne.css": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/websiteexterne.css"}, "Patterns": null}, "WebSiteExterne.html": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/WebSiteExterne.html"}, "Patterns": null}, "websiteexterne.js": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/websiteexterne.js"}, "Patterns": null}, "WebSiteExterneCallLocal.html": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/WebSiteExterneCallLocal.html"}, "Patterns": null}, "websiteExterneTunnel.html": {"Children": null, "Asset": {"ContentRootIndex": 2, "SubPath": "websiteexterneDemo/websiteExterneTunnel.html"}, "Patterns": null}}, "Asset": null, "Patterns": null}, "Core.Themis.Widgets.Offers.styles.css": {"Children": null, "Asset": {"ContentRootIndex": 6, "SubPath": "Core.Themis.Widgets.Offers.styles.css"}, "Patterns": null}}, "Asset": null, "Patterns": [{"ContentRootIndex": 2, "Pattern": "**", "Depth": 0}]}}