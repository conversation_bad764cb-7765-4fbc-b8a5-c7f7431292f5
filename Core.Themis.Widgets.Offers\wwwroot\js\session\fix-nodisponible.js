// Script ULTRA-AGRESSIF pour corriger l'affichage des messages de non-disponibilité

// Injecter CSS ultra-spécifique pour écraser complètement les styles existants
function injectUltraAggressiveCSS() {
    console.log('🔥 INJECTION CSS ULTRA-AGRESSIVE');

    // Supprimer les anciens styles injectés
    $('#ultra-aggressive-noevent-fix').remove();

    // Créer et injecter le nouveau CSS
    var style = document.createElement('style');
    style.id = 'ultra-aggressive-noevent-fix';
    style.innerHTML = `
        /* SOLUTION AVEC SPÉCIFICITÉ MAXIMALE - Garder le style existant mais changer juste la couleur */
        div#oneManifs div#calendarCategsPricesWrapper div#calendarWrapper div#alleventhours .noevent,
        #oneManifs #calendarCategsPricesWrapper #calendarWrapper #alleventhours .noevent,
        #calendarCategsPricesWrapper #calendarWrapper #alleventhours .noevent,
        #calendarWrapper #alleventhours .noevent,
        #alleventhours .noevent.noevent,
        #alleventhours span.noevent {
            background: transparent !important;
            color: #212529 !important;
            line-height: 40px !important;
            padding: 0 6% !important;
            display: block !important;
            border-radius: 1000px !important;
        }

        /* Hover state avec spécificité maximale */
        div#oneManifs div#calendarCategsPricesWrapper div#calendarWrapper div#alleventhours .noevent:hover,
        #oneManifs #calendarCategsPricesWrapper #calendarWrapper #alleventhours .noevent:hover,
        #calendarCategsPricesWrapper #calendarWrapper #alleventhours .noevent:hover,
        #calendarWrapper #alleventhours .noevent:hover,
        #alleventhours .noevent.noevent:hover,
        #alleventhours span.noevent:hover {
            background: transparent !important;
            color: #212529 !important;
        }
    `;

    document.head.appendChild(style);
    console.log('✅ CSS ULTRA-AGRESSIF injecté avec succès');
}

// Fonction AGRESSIVE pour corriger les éléments .noevent
function fixNoeventElements() {
    console.log('🔧 Fix nodisponible - Application AGRESSIVE du style correct');

    // Cibler les éléments .noevent dans #alleventhours
    $('#alleventhours .noevent').each(function() {
        var $el = $(this);
        var element = this;
        var text = $el.text().trim();

        if (text && text.length > 10) { // Si c'est bien notre texte
            console.log('🔧 Correction AGRESSIVE de: ' + text.substring(0, 50) + '...');

            // MÉTHODE 1: Appliquer via jQuery
            $el.css({
                'background': 'transparent',
                'color': '#212529',
                'line-height': '40px',
                'padding': '0 6%',
                'display': 'block',
                'border-radius': '1000px'
            });

            // MÉTHODE 2: Forcer via setProperty avec !important
            if (element && element.style) {
                element.style.setProperty('background', 'transparent', 'important');
                element.style.setProperty('color', '#212529', 'important');
                element.style.setProperty('line-height', '40px', 'important');
                element.style.setProperty('padding', '0 6%', 'important');
                element.style.setProperty('display', 'block', 'important');
                element.style.setProperty('border-radius', '1000px', 'important');
            }

            // MÉTHODE 3: Forcer via cssText
            var cssText = 'background: transparent !important; ' +
                         'color: #212529 !important; ' +
                         'line-height: 40px !important; ' +
                         'padding: 0 6% !important; ' +
                         'display: block !important; ' +
                         'border-radius: 1000px !important;';

            if (element && element.style) {
                element.style.cssText += cssText;
            }

            console.log('✅ Style AGRESSIF appliqué avec succès');
        }
    });

    console.log('🎯 Fix nodisponible - Terminé');
}

// CORRECTION SIMPLE ET EFFICACE au chargement du script
$(document).ready(function() {
    console.log('🚀 Fix nodisponible: Script chargé');

    // ÉTAPE 1: Injecter le CSS IMMÉDIATEMENT
    injectUltraAggressiveCSS();

    // ÉTAPE 2: Appliquer la correction JavaScript IMMÉDIATEMENT
    fixNoeventElements();

    // ÉTAPE 3: Réappliquer après quelques délais pour les éléments chargés dynamiquement
    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 100);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 500);

    setTimeout(function() {
        injectUltraAggressiveCSS();
        fixNoeventElements();
    }, 1000);

    // ÉTAPE 4: Observer les changements DOM pour les nouveaux éléments
    if (window.MutationObserver) {
        var observer = new MutationObserver(function(mutations) {
            var shouldReapply = false;
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    $(mutation.addedNodes).each(function() {
                        if ($(this).hasClass && $(this).hasClass('noevent')) {
                            shouldReapply = true;
                        }
                        if ($(this).find && $(this).find('.noevent').length > 0) {
                            shouldReapply = true;
                        }
                    });
                }
            });

            if (shouldReapply) {
                console.log('🔄 Mutation détectée - Réapplication du fix');
                setTimeout(function() {
                    injectUltraAggressiveCSS();
                    fixNoeventElements();
                }, 50);
            }
        });

        var target = document.getElementById('alleventhours');
        if (target) {
            observer.observe(target, { childList: true, subtree: true });
            console.log('👁️ Observer installé sur #alleventhours');
        }
    }

    console.log('🎯 Fix nodisponible: Configuration terminée');
});
